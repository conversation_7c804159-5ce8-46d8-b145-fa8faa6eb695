import React from 'react';
import Head<PERSON> from './Header';
import ChessmasterCard from './ChessmasterCard';
import GameModeCard from './GameModeCard';

const Dashboard = () => {
  const handleGameMode = (mode) => {
    console.log(`Selected game mode: ${mode}`);
    // Add navigation logic here
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-900 via-purple-800 to-indigo-900">
      <div className="max-w-md mx-auto">
        {/* Header */}
        <Header userName="Chess Player" />
        
        {/* Main Content */}
        <div className="px-4 pb-8">
          {/* Chessmaster Promotion Card */}
          <ChessmasterCard />
          
          {/* Game Mode Grid */}
          <div className="grid grid-cols-2 gap-4">
            {/* Play vs Robot */}
            <GameModeCard
              title="Play Vs Robot"
              icon="🤖"
              bgColor="bg-purple-600"
              onClick={() => handleGameMode('robot')}
            />
            
            {/* Score */}
            <GameModeCard
              title="Score"
              icon="🏆"
              bgColor="bg-yellow-500"
              textColor="text-purple-900"
              onClick={() => handleGameMode('score')}
            />
            
            {/* Play vs Friend */}
            <GameModeCard
              title="Play Vs Friend"
              icon="👥"
              bgColor="bg-teal-500"
              onClick={() => handleGameMode('friend')}
            />
            
            {/* Learn to Learn */}
            <GameModeCard
              title="Learn To Learn"
              icon="📚"
              bgColor="bg-blue-500"
              onClick={() => handleGameMode('learn')}
            />
          </div>
        </div>
        
        {/* Bottom Navigation Placeholder */}
        <div className="fixed bottom-0 left-1/2 transform -translate-x-1/2 w-full max-w-md bg-purple-800/50 backdrop-blur-lg p-4">
          <div className="flex justify-around items-center">
            <button className="p-3 rounded-full bg-purple-600 text-white">
              <div className="w-6 h-6 flex items-center justify-center">🏠</div>
            </button>
            <button className="p-3 rounded-full hover:bg-purple-700 text-white/70">
              <div className="w-6 h-6 flex items-center justify-center">📊</div>
            </button>
            <button className="p-3 rounded-full hover:bg-purple-700 text-white/70">
              <div className="w-6 h-6 flex items-center justify-center">📅</div>
            </button>
            <button className="p-3 rounded-full hover:bg-purple-700 text-white/70">
              <div className="w-6 h-6 flex items-center justify-center">👤</div>
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Dashboard;
