import"./chunk-E562WLSY.mjs";import"./chunk-G32FJCSR.mjs";import"./chunk-HTB5LLOP.mjs";function i(r){let n={};for(let[e,t]of Object.entries(r??{}))if(e!=="__CSS_VALUES__")if(typeof t=="object"&&t!==null)for(let[o,f]of Object.entries(i(t)))n[`${e}${o==="DEFAULT"?"":`-${o}`}`]=f;else n[e]=t;if("__CSS_VALUES__"in r)for(let[e,t]of Object.entries(r.__CSS_VALUES__))(Number(t)&4)===0&&(n[e]=r[e]);return n}export{i as default};
