import React from 'react';

const GameModeCard = ({ 
  title, 
  icon, 
  bgColor = "bg-purple-600", 
  textColor = "text-white",
  onClick,
  size = "normal" // normal or large
}) => {
  const cardClasses = size === "large" 
    ? "col-span-2 h-32" 
    : "h-32";

  return (
    <button
      onClick={onClick}
      className={`${bgColor} ${cardClasses} rounded-3xl p-4 flex flex-col justify-between hover:scale-105 transition-transform duration-200 relative overflow-hidden`}
    >
      {/* Icon */}
      <div className="flex justify-center items-center flex-1">
        <div className={`text-4xl ${textColor}`}>
          {icon}
        </div>
      </div>
      
      {/* Title */}
      <div className="text-center">
        <h3 className={`${textColor} font-semibold text-sm`}>
          {title}
        </h3>
      </div>
    </button>
  );
};

export default GameModeCard;
