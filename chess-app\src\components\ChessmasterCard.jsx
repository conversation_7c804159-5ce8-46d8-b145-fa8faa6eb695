import React from 'react';

const ChessmasterCard = () => {
  return (
    <div className="bg-gradient-to-r from-yellow-400 to-orange-400 rounded-3xl p-6 mb-6 relative overflow-hidden">
      {/* Background Chess Pieces */}
      <div className="absolute right-4 top-4 opacity-20">
        <div className="text-6xl">♔</div>
      </div>
      <div className="absolute right-8 bottom-2 opacity-15">
        <div className="text-4xl">♕</div>
      </div>
      
      {/* Content */}
      <div className="relative z-10">
        <h2 className="text-white text-xl font-bold mb-2 leading-tight">
          Let's Become<br />
          a New<br />
          Chessmaster
        </h2>
        
        <button className="bg-purple-600 hover:bg-purple-700 text-white px-6 py-3 rounded-2xl font-semibold transition-colors mt-4">
          Puzzles
        </button>
      </div>
    </div>
  );
};

export default ChessmasterCard;
