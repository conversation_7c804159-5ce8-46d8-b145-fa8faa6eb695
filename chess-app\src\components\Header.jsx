import React from 'react';

const Header = ({ userName = "Chess Player", userImage = null }) => {
  return (
    <div className="flex items-center justify-between p-4 pt-8">
      {/* Profile Section */}
      <div className="flex items-center space-x-3">
        <div className="w-12 h-12 rounded-full bg-gradient-to-br from-orange-400 to-pink-500 flex items-center justify-center">
          {userImage ? (
            <img src={userImage} alt={userName} className="w-full h-full rounded-full object-cover" />
          ) : (
            <span className="text-white font-semibold text-lg">
              {userName.charAt(0).toUpperCase()}
            </span>
          )}
        </div>
        <div>
          <p className="text-white font-medium text-lg">{userName}</p>
        </div>
      </div>

      {/* Menu Dots */}
      <div className="flex items-center space-x-1">
        <button className="p-2 rounded-full hover:bg-white/10 transition-colors">
          <div className="w-1 h-1 bg-white rounded-full"></div>
          <div className="w-1 h-1 bg-white rounded-full mt-1"></div>
          <div className="w-1 h-1 bg-white rounded-full mt-1"></div>
        </button>
      </div>
    </div>
  );
};

export default Header;
